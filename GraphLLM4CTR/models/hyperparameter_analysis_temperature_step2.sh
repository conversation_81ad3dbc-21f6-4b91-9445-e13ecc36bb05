#!/usr/bin/env bash

# Script to run CTR prediction using embeddings from temperature analysis
# Usage: ./hyperparameter_analysis_temperature_step2.sh

# Base directories
DATA_DIR="/data/datasets/processed_datasets/bookcrossing"
TEMP_ANALYSIS_DIR="${DATA_DIR}/temperature_analysis_FAST"
RESULTS_DIR="${TEMP_ANALYSIS_DIR}/ctr_results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="${RESULTS_DIR}/temperature_ctr_analysis_${TIMESTAMP}.log"

# CTR training parameters
EPOCHS=15
BATCH_SIZE=128
LEARNING_RATE=5e-4
WEIGHT_DECAY=1e-5
EARLY_STOP_PATIENCE=5
WARMUP_STEPS=500
MAX_GRAD_NORM=1.0
SEED=42
DEVICE="cuda"
DATASET_TYPE="bookcrossing"

# Create results directory
mkdir -p "${RESULTS_DIR}"
touch "${LOG_FILE}"

echo "Starting CTR training for different temperature values" | tee -a "${LOG_FILE}"
echo "Timestamp: ${TIMESTAMP}" | tee -a "${LOG_FILE}"
echo "Results will be saved to: ${RESULTS_DIR}" | tee -a "${LOG_FILE}"
echo "----------------------------------------" | tee -a "${LOG_FILE}"

# Find all temperature directories
TEMP_DIRS=$(find "${TEMP_ANALYSIS_DIR}" -maxdepth 1 -name "temp_*" -type d | sort)

if [ -z "${TEMP_DIRS}" ]; then
    echo "Error: No temperature directories found in ${TEMP_ANALYSIS_DIR}" | tee -a "${LOG_FILE}"
    exit 1
fi

echo "Found temperature directories:" | tee -a "${LOG_FILE}"
for dir in ${TEMP_DIRS}; do
    echo "  - $(basename "${dir}")" | tee -a "${LOG_FILE}"
done
echo "----------------------------------------" | tee -a "${LOG_FILE}"

# Create a summary file for results
SUMMARY_FILE="${RESULTS_DIR}/temperature_summary_${TIMESTAMP}.csv"
echo "Temperature,AUC,LogLoss,Accuracy" > "${SUMMARY_FILE}"

# Run CTR training for each temperature
for TEMP_DIR in ${TEMP_DIRS}; do
    # Extract temperature value from directory name
    TEMP_VALUE=$(basename "${TEMP_DIR}" | sed 's/temp_//')

    echo "========================================" | tee -a "${LOG_FILE}"
    echo "Running CTR training for Temperature: ${TEMP_VALUE}" | tee -a "${LOG_FILE}"
    echo "Using embeddings from: ${TEMP_DIR}" | tee -a "${LOG_FILE}"
    echo "========================================" | tee -a "${LOG_FILE}"

    # Create output directory for this temperature's CTR results
    CTR_OUTPUT_DIR="${RESULTS_DIR}/ctr_temp_${TEMP_VALUE}"
    mkdir -p "${CTR_OUTPUT_DIR}"

    # Check if the aligned embeddings directory exists
    EMBEDDING_DIR="${TEMP_DIR}/aligned_embeddings_epoch1"
    if [ ! -d "${EMBEDDING_DIR}" ]; then
        echo "Warning: Embedding directory not found at ${EMBEDDING_DIR}" | tee -a "${LOG_FILE}"
        echo "Checking for final embeddings..." | tee -a "${LOG_FILE}"

        EMBEDDING_DIR="${TEMP_DIR}/aligned_embeddings_final"
        if [ ! -d "${EMBEDDING_DIR}" ]; then
            echo "Error: No embeddings found for temperature ${TEMP_VALUE}" | tee -a "${LOG_FILE}"
            continue
        fi
    fi

    # Check if the qformer checkpoint exists
    QFORMER_CKPT="${TEMP_DIR}/qformer_epoch1.pt"
    if [ ! -f "${QFORMER_CKPT}" ]; then
        echo "Warning: QFormer checkpoint not found at ${QFORMER_CKPT}" | tee -a "${LOG_FILE}"
        echo "Checking for final checkpoint..." | tee -a "${LOG_FILE}"

        QFORMER_CKPT="${TEMP_DIR}/qformer_final.pt"
        if [ ! -f "${QFORMER_CKPT}" ]; then
            echo "Error: No QFormer checkpoint found for temperature ${TEMP_VALUE}" | tee -a "${LOG_FILE}"
            continue
        fi
    fi

    # Run CTR training
    echo "Starting CTR training..." | tee -a "${LOG_FILE}"
    echo "Debug: Using embedding directory: ${EMBEDDING_DIR}" | tee -a "${LOG_FILE}"
    echo "Debug: Using QFormer checkpoint: ${QFORMER_CKPT}" | tee -a "${LOG_FILE}"

    # Verify embeddings exist and show some info
    if [ -f "${EMBEDDING_DIR}/user_graph_proj.pt" ]; then
        echo "Debug: Found user_graph_proj.pt in embedding directory" | tee -a "${LOG_FILE}"
    else
        echo "Debug: WARNING - user_graph_proj.pt not found in ${EMBEDDING_DIR}" | tee -a "${LOG_FILE}"
    fi

    # Create a temporary log file for this specific CTR training run
    CTR_LOG_FILE="${CTR_OUTPUT_DIR}/ctr_training.log"

    python train_ctr_round3.py \
      --train_path "${DATA_DIR}/train.csv" \
      --val_path "${DATA_DIR}/val.csv" \
      --test_path "${DATA_DIR}/test.csv" \
      --embedding_dir "${EMBEDDING_DIR}" \
      --item_meta_path "${DATA_DIR}/train.csv" \
      --qformer_ckpt "${QFORMER_CKPT}" \
      --epochs "${EPOCHS}" \
      --batch_size "${BATCH_SIZE}" \
      --lr "${LEARNING_RATE}" \
      --weight_decay "${WEIGHT_DECAY}" \
      --early_stop_patience "${EARLY_STOP_PATIENCE}" \
      --warmup_steps "${WARMUP_STEPS}" \
      --max_grad_norm "${MAX_GRAD_NORM}" \
      --seed "${SEED}" \
      --log_dir "${CTR_OUTPUT_DIR}" \
      --device "${DEVICE}" \
      --wandb_name "ctr_temp_${TEMP_VALUE}" \
      --dataset_type "${DATASET_TYPE}" 2>&1 | tee "${CTR_LOG_FILE}"

    # Extract results from the CTR training log file
    echo "Extracting results..." | tee -a "${LOG_FILE}"

    # Get the last line with test results from the CTR training log
    TEST_RESULTS=$(grep -a "\[Test Results\]" "${CTR_LOG_FILE}" | tail -n 1)

    # Debug: Show what we found
    echo "Debug: Looking for test results in ${CTR_LOG_FILE}" | tee -a "${LOG_FILE}"
    echo "Debug: Found test results line: ${TEST_RESULTS}" | tee -a "${LOG_FILE}"

    if [ -n "${TEST_RESULTS}" ]; then
        # Extract metrics
        AUC=$(echo "${TEST_RESULTS}" | grep -oP "AUC: \K[0-9.]+")
        ACCURACY=$(echo "${TEST_RESULTS}" | grep -oP "Accuracy: \K[0-9.]+")
        LOGLOSS=$(echo "${TEST_RESULTS}" | grep -oP "LogLoss: \K[0-9.]+")

        # Debug: Show extracted values
        echo "Debug: Extracted AUC=${AUC}, ACCURACY=${ACCURACY}, LOGLOSS=${LOGLOSS}" | tee -a "${LOG_FILE}"

        # Add to summary
        echo "${TEMP_VALUE},${AUC},${LOGLOSS},${ACCURACY}" >> "${SUMMARY_FILE}"

        echo "Results for temperature ${TEMP_VALUE}:" | tee -a "${LOG_FILE}"
        echo "  AUC: ${AUC}" | tee -a "${LOG_FILE}"
        echo "  Accuracy: ${ACCURACY}" | tee -a "${LOG_FILE}"
        echo "  LogLoss: ${LOGLOSS}" | tee -a "${LOG_FILE}"
    else
        echo "Warning: Could not extract test results for temperature ${TEMP_VALUE}" | tee -a "${LOG_FILE}"
        echo "Debug: Checking if CTR log file exists and showing last few lines:" | tee -a "${LOG_FILE}"
        if [ -f "${CTR_LOG_FILE}" ]; then
            echo "Debug: Last 10 lines of ${CTR_LOG_FILE}:" | tee -a "${LOG_FILE}"
            tail -n 10 "${CTR_LOG_FILE}" | tee -a "${LOG_FILE}"
        else
            echo "Debug: CTR log file ${CTR_LOG_FILE} does not exist!" | tee -a "${LOG_FILE}"
        fi
        echo "${TEMP_VALUE},N/A,N/A,N/A" >> "${SUMMARY_FILE}"
    fi

    echo "----------------------------------------" | tee -a "${LOG_FILE}"
done

echo "All CTR training completed!" | tee -a "${LOG_FILE}"
echo "Summary of results saved to: ${SUMMARY_FILE}" | tee -a "${LOG_FILE}"

# Print summary table
echo "Temperature Analysis Summary:" | tee -a "${LOG_FILE}"
echo "----------------------------------------" | tee -a "${LOG_FILE}"
cat "${SUMMARY_FILE}" | tee -a "${LOG_FILE}"
echo "----------------------------------------" | tee -a "${LOG_FILE}"

echo "Script completed successfully!"
